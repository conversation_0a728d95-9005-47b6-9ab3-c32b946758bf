# Task Completion Checklist

When completing a task, always perform the following checks:

## Code Quality Checks
1. Run linting to ensure code follows style guidelines:
   ```bash
   pnpm lint
   ```

2. Run type checking to ensure TypeScript types are correct:
   ```bash
   pnpm type-check
   ```

3. Ensure all new code follows the established code style conventions:
   - Use double quotes for strings
   - Add semicolons at the end of statements
   - Use trailing commas for arrays, objects, and function parameters
   - Maintain 2 space indentation
   - Keep lines under 80 characters
   - Use named exports over default exports
   - Add TypeScript types for all functions and components
   - Use PascalCase for components and interfaces
   - Use camelCase for variables and functions

## Database Operations
1. If database schema changes were made:
   ```bash
   pnpm db:generate
   pnpm db:migrate
   ```

2. Ensure all database operations go through the `prisma` package

## Shared Code
1. If adding shared components/utilities:
   - Place them in the `ui` package
   - Use workspace aliases for import paths when possible

## Testing
1. No test files found in codebase at this time

## Final Verification
1. Build the relevant packages to ensure no compilation errors:
   ```bash
   pnpm build
   ```

2. If working on a specific app, build only that app:
   ```bash
   pnpm build --filter=<app-name>
   ```

3. Verify that the application still functions correctly after changes