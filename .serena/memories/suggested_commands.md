# Suggested Commands

## Development Commands

### Start Applications
```bash
# Start both applications in development mode
pnpm dev

# Start client app only (port 3000)
pnpm dev:display

# Start config app only (port 3001)
pnpm dev:config
```

### Build Commands
```bash
# Build all packages
pnpm build

# Build specific app
pnpm build --filter=client-app
pnpm build --filter=config-app
```

### Code Quality Commands
```bash
# Lint all packages
pnpm lint

# Type check all packages
pnpm type-check

# Clean all build artifacts
pnpm clean
```

### Database Commands
```bash
# Generate Prisma client
pnpm db:generate

# Push schema changes to database
pnpm db:push

# Run database migrations
pnpm db:migrate
```

## Package Management
```bash
# Install dependencies
pnpm install

# Add a dependency to a specific package
pnpm --filter <package-name> add <dependency>

# Add a dependency to the workspace root
pnpm add -w <dependency>
```

## Deployment Commands
```bash
# Build for production
pnpm build --filter=client-app
pnpm build --filter=config-app

# Start production servers
cd packages/client-app && pnpm start
cd packages/config-app && pnpm start
```