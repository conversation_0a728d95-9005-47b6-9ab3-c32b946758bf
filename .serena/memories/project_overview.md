# Project Overview

## Purpose
This is a Next.js blog application built as a Turborepo monorepo with separate applications for content display (client-app) and content management (config-app).

## Tech Stack
- Framework: Next.js
- Language: TypeScript
- Monorepo Tool: Turborepo with PNPM workspaces
- Database: Prisma ORM
- Authentication: NextAuth.js
- Styling: Tailwind CSS
- Payments: Stripe
- Content Management: Custom dashboard with markdown editor
- Image Handling: AWS S3 with on-the-fly resizing
- Internationalization: AI-powered translation
- SEO: Automatic SEO generation for articles

## Applications
1. Client App (Port 3000) - Public-facing blog content display
2. Config App (Port 3001) - Admin dashboard for content management
3. Shared packages:
   - UI Package: Shared components and utilities
   - Prisma Package: Database schema and client
   - TypeScript Config: Shared TypeScript configurations
   - Scripts: Shared scripts