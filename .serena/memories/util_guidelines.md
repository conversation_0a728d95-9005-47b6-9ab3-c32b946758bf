# Utility Guidelines and System Commands

## System Commands (Darwin)
- Use `ls` for listing directory contents
- Use `cd` for changing directories
- Use `grep` for searching within files
- Use `find` for locating files
- Use `git` for version control operations

## Development Utilities
- All development commands should use `pnpm` as the package manager
- Use Turborepo commands for managing the monorepo:
  - `pnpm dev` to start development servers
  - `pnpm build` to build packages
  - `pnpm lint` for code linting
  - `pnpm type-check` for TypeScript type checking

## File Operations
- Use workspace aliases for import paths when possible
- Shared components should be placed in the `ui` package
- Database operations must go through the `prisma` package
- Utility functions should be placed in the `ui` package when appropriate

## Third-Party Libraries
- Use Context7 MCP when implementing third-party libraries
- Use shadcn-ui MCP for component library implementation
- Always check if a library is already used in the project before adding new dependencies

## Code Reuse Prevention
- Before writing new code, check if similar functionality already exists
- Use the search tools to find existing implementations
- Prefer extending existing components/utilities over creating new ones
- Always look for opportunities to refactor and consolidate code