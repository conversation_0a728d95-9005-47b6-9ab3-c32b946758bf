# Code Style and Conventions

## General Code Style
- Use double quotes for strings
- Semicolons required at the end of statements
- Trailing commas for all applicable items (arrays, objects, function parameters)
- 2 space indentation
- 80 character line limit
- Named exports preferred over default exports
- TypeScript types required for all functions and components
- Use PascalCase for components and interfaces
- Use camelCase for variables and functions
- Error handling with try/catch for async operations

## Import Conventions
- Import paths use workspace aliases when possible
- Shared components in `ui` package
- Database operations through `prisma` package
- Group imports logically (external, workspace, local)
- Absolute imports preferred over relative imports when possible

## Component Development
- Use TypeScript for all components
- Define prop interfaces for all components
- Use functional components with React hooks
- Implement proper error boundaries when necessary
- Follow accessibility best practices

## Database Operations
- All database operations must go through the `prisma` package
- Use Prisma's type-safe queries
- Handle database errors appropriately
- Use transactions for related operations

## Shared Code
- Shared components should be in the `ui` package
- Utility functions should be in the `ui` package
- Reusable hooks should be in the `ui` package
- Common TypeScript configurations in `typescript-config` package

## Testing
- No test files found in codebase at this time

## Formatting
- Prettier formatting enforced via ESLint
- Next.js linting rules with core-web-vitals
- React prop-types disabled
- Console warnings enabled

## Additional Rules
- Use Context7 MCP when using third-party libraries
- Use shadcn-ui MCP for component library implementation