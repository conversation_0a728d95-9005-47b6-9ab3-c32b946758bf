# CRUSH.md

## Build/Lint/Test Commands
- `pnpm dev` - Start all apps in development mode
- `pnpm dev:display` - Start client app only (port 3000)
- `pnpm dev:config` - Start config app only (port 3001)
- `pnpm build` - Build all packages
- `pnpm lint` - Lint all packages
- `pnpm type-check` - Type check all packages
- No test files found in codebase

## Code Style Guidelines
- Use double quotes for strings
- Semicolons required
- Trailing commas for all applicable items
- 2 space indentation
- 80 character line limit
- Named exports preferred over default exports
- TypeScript types required for all functions and components
- Use PascalCase for components and interfaces
- Use camelCase for variables and functions
- Error handling with try/catch for async operations
- Import paths use workspace aliases when possible
- Shared components in `ui` package
- Database operations through `prisma` package

## Formatting
- Prettier formatting enforced via ESLint
- Next.js linting rules with core-web-vitals
- React prop-types disabled
- Console warnings enabled

## Serena MCP Usage
- Use `find_symbol` to locate specific functions, classes, or components in the codebase
- Use `find_referencing_symbols` to find all usages of a specific symbol
- Use `search_for_pattern` for complex code searches across the project
- Use `get_symbols_overview` to understand the structure of a file before editing
- Use `read_memory` to access project-specific conventions and guidelines
- Use `list_memories` to see what helpful information is available
- Use `replace_symbol_body` to update function or component implementations
- Use `insert_before_symbol` or `insert_after_symbol` for adding new code

## Additional Rules
- Use Context7 MCP when using third-party libraries
- Use shadcn-ui MCP for component library implementation
- Database operations must go through the `prisma` package
- Shared components should be in the `ui` package
- Use workspace aliases for import paths when possible
- Leverage Serena's project memories for understanding code conventions