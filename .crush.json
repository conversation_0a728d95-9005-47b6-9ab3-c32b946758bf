{"$schema": "https://charm.land/crush.json", "mcp": {"context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "shadcn-ui": {"type": "stdio", "command": "npx", "args": ["-y", "@jpisnice/shadcn-ui-mcp-server"]}, "serena": {"type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena", "start-mcp-server"]}}, "lsp": {"typescript": {"command": "typescript-language-server", "args": ["--st<PERSON>"]}}}