
# Next.js Blog Application

This is a blog application built with Next.js, featuring user authentication, content management, and Stripe integration for subscriptions.

## Key Technologies

- **Framework**: Next.js
- **Language**: TypeScript
- **Authentication**: NextAuth.js
- **Database**: Prisma
- **Styling**: Tailwind CSS
- **Payments**: Stripe
- **Content Management**: A custom dashboard with a markdown editor allows for creating, editing, and managing blog posts.
- **Image Handling**: Images are stored in AWS S3 and resized on-the-fly for different use cases.
- **Internationalization**: The application supports multiple languages, with content translated using AI.
- **SEO**: SEO information is automatically generated for each article.

## Getting Started

To run the application locally, follow these steps:

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   Create a `.env` file in the root of the project and add the following environment variables:
   ```
   DATABASE_URL="your-database-url"
   NEXTAUTH_SECRET="your-nextauth-secret"
   STRIPE_SECRET_KEY="your-stripe-secret-key"
   STRIPE_WEBHOOK_SECRET="your-stripe-webhook-secret"
   AWS_ACCESS_KEY_ID="your-aws-access-key-id"
   AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
   AWS_S3_BUCKET_NAME="your-aws-s3-bucket-name"
   OPENAI_API_KEY="your-openai-api-key"
   ```

3. **Run database migrations**:
   ```bash
   npx prisma migrate dev
   ```

4. **Run the development server**:
   ```bash
   npm run dev
   ```

5. **Start the Stripe webhook listener**:
   ```bash
   npm run dev:stripe
   ```

## Features

### Content Management

- **Create, Edit, and Delete Blog Posts**: A user-friendly dashboard allows administrators to manage blog posts.
- **Markdown Editor**: A rich markdown editor with preview functionality is used for writing and editing blog content.
- **Image Upload**: Images can be uploaded and inserted into blog posts.
- **SEO Suggestions**: The application provides SEO suggestions for articles to improve their search engine rankings.
- **Translation**: Articles can be translated into different languages using AI.

### User Authentication

- **Social Login**: Users can sign in using their Google or GitHub accounts.
- **Role-Based Access Control**: The application has a simple role-based access control system, with an `ADMIN` role for managing content.

### Subscriptions

- **Stripe Integration**: The application uses Stripe to handle subscriptions.
- **Checkout and Billing Management**: Users can subscribe to a plan and manage their billing information.

### Internationalization

- **Multi-language Support**: The application is designed to support multiple languages.
- **AI-powered Translation**: Articles can be automatically translated into different languages.

### Data Fetching

- **Prisma ORM**: The application uses Prisma to interact with the database.
- **Article Queries**: Articles can be queried by slug, ID, or a list of all published articles for static site generation.
