import { OpenAI } from "openai";
import { ImageBucket } from "../s3";

export async function generateImage(prompt: string, openaiApiKey?: string) {
  const apiKey = openaiApiKey || process.env.OPENAI_API_KEY;

  if (!apiKey) {
    throw new Error("OpenAI API key not configured");
  }

  const openai = new OpenAI({
    // organization: "org-n86qx2jcSuDnWrXLDpHpGGqS",
    apiKey,
  });
  try {
    const response = await openai.images.generate({
      model: "dall-e-3",
      prompt,
      n: 1,
      size: "1792x1024",
    });

    response?.data?.forEach(async (entry) => {
      console.log(entry);

      if (!entry?.url) return;
      const fileName = new URL(entry.url).pathname.split("/").pop() ?? "";

      const bucket = new ImageBucket();

      const imageBuffer = (await fetch(entry.url).then((res) =>
        res.arrayBuffer(),
      )) as Buffer;
      await bucket.upload({
        key: `generated-image/${fileName}`,
        buffer: imageBuffer,
      });

      const jsonBuffer = Buffer.from(JSON.stringify(entry));
      await bucket.upload({
        key: `generated-image/${fileName}.json`,
        buffer: jsonBuffer,
      });
    });
  } catch (error) {
    return { error };
  }
}
