import { StructuredOutputParser } from "@langchain/core/output_parsers";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { z } from "zod";

export async function generateSeoInfo({
  lang,
  title,
  article,
}: {
  lang: string;
  title: string;
  article: string;
}) {
  try {
    const model = new ChatGoogleGenerativeAI({ model: "gemini-2.5-flash" });

    const PROMPT_TEMPLATE = `
  - Given title: {title}
  - Given article:
    ---
    {article}
    ---
  - Your are an SEO expert. According to given info compose an url slug in english and a description in {lang} optimized for SEO according to the given info
  - {format_instructions}
  `;

    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        slug: z.string().describe("slug for the article"),
        title: z.string().describe("seo title"),
        description: z.string().describe("seo description"),
      }),
    );

    const prompt = new PromptTemplate({
      template: PROMPT_TEMPLATE,
      inputVariables: ["lang", "title", "article"],
      partialVariables: {
        format_instructions: parser.getFormatInstructions(),
        lang,
      },
    });

    const llmChain = prompt.pipe(model).pipe(parser);
    const result = await llmChain.invoke({ lang, title, article });
    return result;
  } catch (err) {
    console.error(err);
  }
}
