import { StructuredOutputParser } from "@langchain/core/output_parsers";
import { PromptTemplate } from "@langchain/core/prompts";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { z } from "zod";

interface translateArticleProps {
  targetLanguage: string;
  article: {
    title: string;
    content: string;
    description: string;
  };
}

export async function translateArticle({
  targetLanguage,
  article,
}: translateArticleProps) {
  try {
    const model = new ChatGoogleGenerativeAI({ model: "gemini-2.5-flash" });

    const { title, content, description } = article;

    const PROMPT_TEMPLATE = `
   - Given content:
    ---
    title: {title}
    content: {content}
    description: {description}
    ---
  - Your are a financial expert. Please translate given content into {targetLanguage}
  - {format_instructions}
  `;

    const parser = StructuredOutputParser.fromZodSchema(
      z.object({
        title: z.string().describe("article title"),
        content: z.string().describe("article markdown content"),
        description: z.string().describe("seo description"),
      }),
    );

    const prompt = new PromptTemplate({
      template: PROMPT_TEMPLATE,
      inputVariables: ["title", "content", "description"],
      partialVariables: {
        format_instructions: parser.getFormatInstructions(),
        targetLanguage,
      },
    });

    const llmChain = prompt.pipe(model).pipe(parser);

    const result = await llmChain.invoke({ title, content, description });

    return result;
  } catch (err) {
    console.error(err);
  }
}
