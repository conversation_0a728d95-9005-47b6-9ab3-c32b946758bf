import {
  DeleteObjectCommand,
  ListObjectsV2Command,
  PutObjectCommand,
  S3Client,
  S3ServiceException,
} from "@aws-sdk/client-s3";

/**
 * Upload a file to an S3 bucket.
 */

export class ImageBucket {
  private client: S3Client | null = null;
  private config: {
    bucketName?: string;
    region?: string;
    accessKeyId?: string;
    secretAccessKey?: string;
  } | null = null;

  constructor(config?: {
    bucketName?: string;
    region?: string;
    accessKeyId?: string;
    secretAccessKey?: string;
  }) {
    this.config = config || null;
  }

  private async getClient(): Promise<S3Client> {
    if (this.client) {
      return this.client;
    }

    let bucketName, region, accessKeyId, secretAccessKey;

    if (this.config) {
      // Use provided config
      ({ bucketName, region, accessKeyId, secretAccessKey } = this.config);
    } else {
      // Fallback to environment variables for backward compatibility
      const env = process.env;
      bucketName = env.S3_BUCKET_NAME;
      region = env.AWS_REGION;
      accessKeyId = env.AWS_ACCESS_KEY_ID;
      secretAccessKey = env.AWS_SECRET_ACCESS_KEY;
    }

    if (!bucketName || !region || !accessKeyId || !secretAccessKey) {
      throw new Error("Missing S3 configuration");
    }

    this.client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    return this.client;
  }

  listFiles = async () => {
    const bucketName = this.config?.bucketName || process.env.S3_BUCKET_NAME;
    if (!bucketName) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new ListObjectsV2Command({
      Bucket: bucketName,
    });

    const client = await this.getClient();
    const response = await client.send(command);
    console.log(response);
    return response;
  };

  uploadFile = async ({
    key,
    file,
    maxSizeBytes = 5 * 1024 * 1024, // Default 5MB limit
  }: {
    key: string;
    file: File;
    maxSizeBytes?: number;
  }) => {
    // Validate file size
    const fileSize = file.size || (await file.arrayBuffer?.())?.byteLength || 0;
    if (fileSize > maxSizeBytes) {
      throw new Error(
        `File size ${(fileSize / 1024 / 1024).toFixed(
          2,
        )}MB exceeds maximum allowed size ${(
          maxSizeBytes /
          1024 /
          1024
        ).toFixed(2)}MB`,
      );
    }

    const buffer =
      file instanceof Buffer ? file : Buffer.from(await file.arrayBuffer());

    await this.upload({ key: `blog-image/${key}`, buffer });
  };

  upload = async ({ key, buffer }: { key: string; buffer: Buffer }) => {
    const bucketName = this.config?.bucketName || process.env.S3_BUCKET_NAME;
    if (!bucketName) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: buffer,
    });

    try {
      const client = await this.getClient();
      const response = await client.send(command);
      console.log(response);
    } catch (caught) {
      if (
        caught instanceof S3ServiceException &&
        caught.name === "EntityTooLarge"
      ) {
        console.error(
          `Error from S3 while uploading object to ${bucketName}. \
  The object was too large. To upload objects larger than 5GB, use the S3 console (160GB max) \
  or the multipart upload API (5TB max).`,
        );
      } else if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while uploading object to ${bucketName}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };

  deleteFile = async ({ key }: { key: string }) => {
    const bucketName = this.config?.bucketName || process.env.S3_BUCKET_NAME;
    if (!bucketName) {
      throw new Error("S3 bucket name not configured");
    }

    const command = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    try {
      const client = await this.getClient();
      const response = await client.send(command);
      console.log(response);
    } catch (caught) {
      if (caught instanceof S3ServiceException) {
        console.error(
          `Error from S3 while deleting object to ${bucketName}.  ${caught.name}: ${caught.message}`,
        );
      } else {
        throw caught;
      }
    }
  };
}
