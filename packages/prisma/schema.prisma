generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String          @id @default(cuid())
  name                 String?
  email                String          @unique
  emailVerified        DateTime?
  image                String?
  role                 Role            @default(USER)
  createdAt            DateTime        @default(now())
  updatedAt            DateTime        @updatedAt
  subscriptionStatus   Boolean         @default(false)
  stripeSubscriptionId String?
  stripeCustomerId     String?
  accounts             Account[]
  Authenticator        Authenticator[]
  sessions             Session[]
  spaces               Space[]
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

enum Role {
  ADMIN
  AUTHOR
  USER
}

model Space {
  id          String     @id @default(cuid())
  title       String
  description String
  ownerId     String
  blogs       Blog[]
  owner       User       @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  Category    Category[]

  // Configuration fields
  authGoogleId                    String?
  authGoogleSecret                String?
  s3BucketName                    String?
  awsRegion                       String?
  awsAccessKeyId                  String?
  awsSecretAccessKey              String?
  gaMeasurementId                 String?
  openaiApiKey                    String?
  googleApiKey                    String?
  nextPublicStripePublishableKey  String?
  stripeSecretKey                 String?
  stripeWebhookSecret             String?
  nextPublicFaviconUrl            String?
}

model Blog {
  id          String        @id @default(cuid())
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  isPremium   Boolean
  isPublished Boolean
  articleDate DateTime
  slug        String
  spaceId     String
  Space       Space         @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  contents    BlogContent[]
  tags        String[]
  categories  Category[]
}

model BlogContent {
  id             String   @id @default(cuid())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  isReady        Boolean  @default(false)
  title          String
  imageUrl       String
  seoDescription String
  content        String
  language       String
  blogId         String?
  Blog           Blog?    @relation(fields: [blogId], references: [id], onDelete: Cascade)
}

model Category {
  id      String          @id @default(cuid())
  spaceId String
  Space   Space           @relation(fields: [spaceId], references: [id], onDelete: Cascade)
  blogs   Blog[]
  labels  CategoryLabel[]
}

model CategoryLabel {
  id         String    @id @default(cuid())
  Category   Category? @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId String?
  label      String
  language   String
}
