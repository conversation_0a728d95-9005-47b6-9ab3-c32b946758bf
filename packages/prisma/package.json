{"name": "prisma-db", "version": "0.1.0", "private": true, "main": "./index.ts", "types": "./index.ts", "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@prisma/client": "^6.13.0"}, "devDependencies": {"@types/node": "^20.19.9", "concurrently": "^9.2.0", "prisma": "^6.13.0", "typescript": "^5", "typescript-config": "workspace:*"}}