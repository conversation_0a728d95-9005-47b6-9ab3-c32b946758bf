-- AlterTable
ALTER TABLE "public"."Space" ADD COLUMN     "authGoogleId" TEXT,
ADD COLUMN     "authGoogleSecret" TEXT,
ADD COLUMN     "awsAccessKeyId" TEXT,
ADD COLUMN     "awsRegion" TEXT,
ADD COLUMN     "awsSecretAccessKey" TEXT,
ADD COLUMN     "gaMeasurementId" TEXT,
ADD COLUMN     "googleApiKey" TEXT,
ADD COLUMN     "nextPublicFaviconUrl" TEXT,
ADD COLUMN     "nextPublicStripePublishableKey" TEXT,
ADD COLUMN     "openaiApiKey" TEXT,
ADD COLUMN     "s3BucketName" TEXT,
ADD COLUMN     "stripeSecretKey" TEXT,
ADD COLUMN     "stripeWebhookSecret" TEXT;

-- AlterTable
ALTER TABLE "public"."_BlogToCategory" ADD CONSTRAINT "_BlogToCategory_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "public"."_BlogToCategory_AB_unique";
