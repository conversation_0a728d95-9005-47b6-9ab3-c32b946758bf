import { auth } from "@/auth";
import { LoginButton } from "@/app/components/stripe/login";
import { PricingSection } from "./pricing-section";

export default async function HomePage() {
  const session = await auth();

  if (session?.user?.role !== "ADMIN") {
    return "You are not authorized to view this page.";
  }

  const renderContent = () => {
    if (!session?.user) {
      return <LoginButton />;
    }

    return <PricingSection />;
  };

  return (
    <main className="w-full max-w-[1920px] min-h-full mx-auto p-5 md:p-10 space-y-10 flex flex-col">
      <div className="mx-auto">{renderContent()}</div>
    </main>
  );
}
