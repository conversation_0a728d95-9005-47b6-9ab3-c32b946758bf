import { StripeCheckout } from "@/app/components/stripe/stripe-checkout";
import { MarkdownPreview } from "@/app/components/markdown/markdown-preview";
import { SessionProvider } from "next-auth/react";

export const BlogContent = ({
  content,
  isPremium,
}: {
  content: string;
  isPremium?: boolean;
}) => {
  if (content) {
    return <MarkdownPreview className="sm:px-10" content={content} />;
  }

  if (isPremium) {
    return (
      <SessionProvider>
        <StripeCheckout />
      </SessionProvider>
    );
  }
};
