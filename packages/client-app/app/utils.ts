export function formatArticlePath({
  id,
  slug,
  lang,
}: {
  id: string;
  slug?: string;
  lang?: string;
}) {
  const path = [lang === "en" ? "" : lang, "article", slug || id]
    .filter(Boolean)
    .join("/");
  return `/${path}`;
}

export function formatDate(date: Date | number | string | null | undefined) {
  if (!date) return "";
  return new Date(date).toDateString();
}

export const LANG_PATHS = [
  {
    label: "English",
    path: "/",
  },
  {
    label: "日本語",
    path: "/ja",
  },
];
