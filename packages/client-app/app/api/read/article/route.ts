import { queryArticleBySlug } from "./query";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const slug = searchParams.get("slug");
  const lang = searchParams.get("lang");

  if (!slug) {
    return Response.json(
      { message: "slug or id is required" },
      { status: 400 },
    );
  }

  try {
    const blog =
      (await queryArticleBySlug(slug, lang)) ||
      (await queryArticleBySlug(slug, lang));
    if (blog) {
      return Response.json(blog);
    }
  } catch (error) {
    return Response.json({ error }, { status: 400 });
  }
}
