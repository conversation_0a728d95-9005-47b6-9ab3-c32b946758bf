import type { Config } from "tailwindcss";
import typography from "@tailwindcss/typography";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "../../packages/ui/src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "../../packages/ui/src/utils/**/*.{js,ts,jsx,tsx,mdx}",
    "../../packages/ui/index.tsx",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: "none",
            color: "inherit",
            a: {
              color: "inherit",
              textDecoration: "underline",
              fontWeight: "500",
            },
            "[class~='lead']": {
              color: "inherit",
            },
            strong: {
              color: "inherit",
              fontWeight: "600",
            },
            "ol > li::before": {
              color: "inherit",
            },
            "ul > li::before": {
              backgroundColor: "currentColor",
            },
            hr: {
              borderColor: "currentColor",
              opacity: 0.3,
            },
            blockquote: {
              color: "inherit",
              borderLeftColor: "currentColor",
              opacity: 0.8,
            },
            h1: {
              color: "inherit",
            },
            h2: {
              color: "inherit",
            },
            h3: {
              color: "inherit",
            },
            h4: {
              color: "inherit",
            },
            "figure figcaption": {
              color: "inherit",
            },
            code: {
              color: "inherit",
            },
            "a code": {
              color: "inherit",
            },
            pre: {
              color: "inherit",
              backgroundColor: "rgba(0, 0, 0, 0.05)",
            },
            thead: {
              color: "inherit",
              borderBottomColor: "currentColor",
            },
            "tbody tr": {
              borderBottomColor: "currentColor",
              opacity: 0.5,
            },
          },
        },
      },
    },
  },
  plugins: [typography],
};

export default config;
