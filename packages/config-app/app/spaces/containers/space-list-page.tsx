"use client";
import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Plus, Settings, Users, FileText, Trash2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import Link from "next/link";

const createSpaceSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});

type CreateSpaceFormValues = z.infer<typeof createSpaceSchema>;

interface Space {
  id: string;
  title: string;
  description: string;
  owner: {
    id: string;
    name: string | null;
    email: string;
  };
  _count: {
    blogs: number;
    Category: number;
  };
}

export function SpaceListPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const form = useForm<CreateSpaceFormValues>({
    resolver: zodResolver(createSpaceSchema),
    defaultValues: {
      title: "",
      description: "",
    },
  });

  const { data: spaces, isLoading } = useQuery({
    queryKey: ["spaces"],
    queryFn: async () => {
      const response = await fetch("/api/manage/spaces");
      if (!response.ok) {
        throw new Error("Failed to fetch spaces");
      }
      return response.json() as Promise<Space[]>;
    },
  });

  const createSpaceMutation = useMutation({
    mutationFn: async (data: CreateSpaceFormValues) => {
      const response = await fetch("/api/manage/spaces", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create space");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Space created",
        description: "Your space has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["spaces"] });
      setCreateDialogOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteSpaceMutation = useMutation({
    mutationFn: async (spaceId: string) => {
      const response = await fetch(`/api/manage/spaces/${spaceId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete space");
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Space deleted",
        description: "The space has been deleted successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["spaces"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: CreateSpaceFormValues) => {
    createSpaceMutation.mutate(data);
  };

  const handleDeleteSpace = (spaceId: string, spaceName: string) => {
    if (
      confirm(
        `Are you sure you want to delete "${spaceName}"? This action cannot be undone.`,
      )
    ) {
      deleteSpaceMutation.mutate(spaceId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-lg">Loading spaces...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Space Management
          </h1>
          <p className="text-muted-foreground">
            Manage your spaces and their configurations
          </p>
        </div>

        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Space
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New Space</DialogTitle>
              <DialogDescription>
                Create a new space to organize your content and configuration.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter space title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter space description"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button
                    type="submit"
                    disabled={createSpaceMutation.isPending}
                  >
                    {createSpaceMutation.isPending
                      ? "Creating..."
                      : "Create Space"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {spaces?.map((space) => (
          <Card key={space.id} className="relative">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{space.title}</CardTitle>
                  <CardDescription className="mt-1">
                    {space.description}
                  </CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteSpace(space.id, space.title)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Owner: {space.owner.name || space.owner.email}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="flex items-center">
                    <FileText className="h-4 w-4 mr-1" />
                    {space._count.blogs} blogs
                  </span>
                  <span className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {space._count.Category} categories
                  </span>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Link href={`/spaces/${space.id}`}>
                      <Settings className="h-4 w-4 mr-1" />
                      Manage
                    </Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Link href={`/spaces/${space.id}/config`}>
                      <Settings className="h-4 w-4 mr-1" />
                      Config
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {spaces?.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-muted-foreground mb-2">
            No spaces found
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            Create your first space to get started.
          </p>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Space
          </Button>
        </div>
      )}
    </div>
  );
}
