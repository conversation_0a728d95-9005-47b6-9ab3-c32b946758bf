"use client";

import { signIn, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";

export function SignIn() {
  return (
    <Button onClick={() => signIn("google", { callbackUrl: "/spaces" })}>
      Sign In
    </Button>
  );
}

export function SignOut() {
  return (
    <Button variant="outline" onClick={() => signOut({ callbackUrl: "/" })}>
      Sign Out
    </Button>
  );
}
