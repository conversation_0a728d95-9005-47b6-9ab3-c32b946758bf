import { auth } from "@/auth";
import { prisma, Prisma } from "prisma-db";

export type CreateContentType = Prisma.BlogContentCreateManyInput;

export async function POST(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const data: CreateContentType = await req.json();
    const content = await prisma.blogContent.create({
      data,
    });

    return Response.json(content);
  } catch (error) {
    return Response.json(
      {
        message: "create content failed",
        error,
      },
      { status: 400 },
    );
  }
}
