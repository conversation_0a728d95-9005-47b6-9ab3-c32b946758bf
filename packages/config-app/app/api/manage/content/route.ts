import { auth } from "@/auth";
import { prisma } from "prisma-db";

const query = (id: string) =>
  prisma.blogContent.findFirst({
    where: { id },
    include: {
      Blog: {
        include: { categories: { select: { id: true } } },
      },
    },
  });

export type BlogGetResponse = Awaited<ReturnType<typeof query>>;

export async function GET(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const id = searchParams.get("id");

  if (!id) {
    return Response.json({ message: "Invalid ID" }, { status: 400 });
  }
  const blog = await query(id);
  return Response.json(blog);
}
