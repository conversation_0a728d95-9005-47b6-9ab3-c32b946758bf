import { auth } from "@/auth";
import { prisma, Prisma } from "prisma-db";

export type CreateBlogInput = {
  blog: Omit<Prisma.BlogCreateManyInput, "spaceId">;
  content: Omit<Prisma.BlogContentCreateManyInput, "blogId">;
};

export async function POST(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const params: { id: string } = await req.json();

  try {
    await prisma.blog.delete({
      where: { id: params.id },
    });

    return Response.json({ message: "success" });
  } catch (error) {
    return Response.json(
      {
        message: "delete blog failed",
        error,
      },
      { status: 400 },
    );
  }
}
