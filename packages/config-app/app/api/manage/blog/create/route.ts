import { auth } from "@/auth";
import { prisma, Prisma } from "prisma-db";

export type CreateBlogInput = {
  blog: Omit<Prisma.BlogCreateManyInput, "spaceId">;
  content: Omit<Prisma.BlogContentCreateManyInput, "blogId">;
};

const spaceId = process.env.SPACE_ID ?? "";

export async function POST(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const data: CreateBlogInput = await req.json();

    const blog = await prisma.blog.create({
      data: {
        ...data.blog,
        spaceId,
      },
    });
    const content = await prisma.blogContent.create({
      data: { ...data.content, blogId: blog.id },
    });

    return Response.json({ blog, content });
  } catch (error) {
    return Response.json(
      {
        message: "create blog failed",
        error,
      },
      { status: 400 },
    );
  }
}
