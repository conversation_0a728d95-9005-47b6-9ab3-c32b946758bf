import { auth } from "@/auth";

const spaceId = process.env.SPACE_ID ?? "";

export async function GET() {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  // Redirect to space-specific config endpoint
  if (spaceId) {
    return Response.redirect(
      new URL(
        `/api/manage/spaces/${spaceId}/config`,
        process.env.NEXTAUTH_URL || "http://localhost:3001",
      ),
    );
  }

  return Response.json(
    { message: "No default space configured" },
    { status: 404 },
  );
}

export async function PUT() {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  // Redirect to space-specific config endpoint
  if (spaceId) {
    return Response.json(
      {
        message: "Please use the space-specific configuration endpoint",
        redirect: `/api/manage/spaces/${spaceId}/config`,
      },
      { status: 301 },
    );
  }

  return Response.json(
    { message: "No default space configured" },
    { status: 404 },
  );
}
