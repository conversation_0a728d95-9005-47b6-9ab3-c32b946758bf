import { auth } from "@/auth";
import { prisma } from "prisma-db";
import { z } from "zod";

const updateSpaceSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});

export type UpdateSpaceData = z.infer<typeof updateSpaceSchema>;

// GET - Get single space (admin only)
export async function GET(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    const space = await prisma.space.findUnique({
      where: { id: spaceId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            blogs: true,
            Category: true,
          },
        },
      },
    });

    if (!space) {
      return Response.json({ message: "Space not found" }, { status: 404 });
    }

    return Response.json(space);
  } catch (error) {
    return Response.json(
      {
        message: "Failed to fetch space",
        error,
      },
      { status: 500 },
    );
  }
}

// PUT - Update space (admin only)
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    const data = await req.json();
    const validatedData = updateSpaceSchema.parse(data);

    const space = await prisma.space.update({
      where: { id: spaceId },
      data: validatedData,
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            blogs: true,
            Category: true,
          },
        },
      },
    });

    return Response.json(space);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid space data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to update space",
        error,
      },
      { status: 500 },
    );
  }
}

// DELETE - Delete space (admin only)
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ spaceId: string }> },
) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { spaceId } = await params;

  try {
    // Check if space exists
    const space = await prisma.space.findUnique({
      where: { id: spaceId },
      include: {
        _count: {
          select: {
            blogs: true,
            Category: true,
          },
        },
      },
    });

    if (!space) {
      return Response.json({ message: "Space not found" }, { status: 404 });
    }

    // Check if space has content
    if (space._count.blogs > 0 || space._count.Category > 0) {
      return Response.json(
        {
          message: "Cannot delete space with existing blogs or categories",
        },
        { status: 400 },
      );
    }

    await prisma.space.delete({
      where: { id: spaceId },
    });

    return Response.json({ message: "Space deleted successfully" });
  } catch (error) {
    return Response.json(
      {
        message: "Failed to delete space",
        error,
      },
      { status: 500 },
    );
  }
}
