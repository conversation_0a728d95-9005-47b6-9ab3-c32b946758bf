import { auth } from "@/auth";
import { prisma } from "prisma-db";
import { z } from "zod";

const createSpaceSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});

const updateSpaceSchema = z.object({
  id: z.string(),
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
});

export type CreateSpaceData = z.infer<typeof createSpaceSchema>;
export type UpdateSpaceData = z.infer<typeof updateSpaceSchema>;

// GET - List all spaces (admin only)
export async function GET() {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const spaces = await prisma.space.findMany({
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            blogs: true,
            Category: true,
          },
        },
      },
      orderBy: {
        title: "asc",
      },
    });

    return Response.json(spaces);
  } catch (error) {
    return Response.json(
      {
        message: "Failed to fetch spaces",
        error,
      },
      { status: 500 },
    );
  }
}

// POST - Create new space (admin only)
export async function POST(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const data = await req.json();
    const validatedData = createSpaceSchema.parse(data);

    const space = await prisma.space.create({
      data: {
        ...validatedData,
        ownerId: res.user.id,
      },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            blogs: true,
            Category: true,
          },
        },
      },
    });

    return Response.json(space);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid space data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to create space",
        error,
      },
      { status: 500 },
    );
  }
}

// PUT - Update space (admin only)
export async function PUT(req: Request) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  try {
    const data = await req.json();
    const validatedData = updateSpaceSchema.parse(data);

    const space = await prisma.space.update({
      where: { id: validatedData.id },
      data: {
        title: validatedData.title,
        description: validatedData.description,
      },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            blogs: true,
            Category: true,
          },
        },
      },
    });

    return Response.json(space);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        {
          message: "Invalid space data",
          errors: error.errors,
        },
        { status: 400 },
      );
    }

    return Response.json(
      {
        message: "Failed to update space",
        error,
      },
      { status: 500 },
    );
  }
}
