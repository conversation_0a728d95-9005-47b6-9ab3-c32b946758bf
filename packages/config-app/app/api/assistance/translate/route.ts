import { NextRequest } from "next/server";

import { auth } from "@/auth";
import { TRANSLATE_CONFIG } from "./constants";
import { prisma } from "prisma-db";
import { translateArticle } from "ui/src/utils";

export async function POST(request: NextRequest) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }

  const { language, contentId } = await request.json();

  const targetLanguage = TRANSLATE_CONFIG.find(
    (entry) => entry.language === language,
  )?.label;

  if (!targetLanguage) {
    return Response.json(
      { message: "target language is not supported" },
      { status: 400 },
    );
  }

  const contentData = await prisma.blogContent.findFirst({
    where: { id: contentId },
  });

  if (!contentData) {
    return Response.json({ message: "fetch article failed" }, { status: 400 });
  }

  const { title, content, seoDescription, imageUrl, blogId } = contentData;

  const article = {
    title,
    content,
    description: seoDescription,
  };

  const translatedArticle = await translateArticle({ targetLanguage, article });

  if (!translatedArticle?.content) {
    return Response.json({ message: "translation failed" }, { status: 400 });
  }

  const response = await prisma.blogContent.create({
    data: {
      blogId,
      language,
      imageUrl,
      title: translatedArticle.title ?? "",
      content: translatedArticle.content ?? "",
      seoDescription: translatedArticle.description ?? "",
    },
  });

  return Response.json(response);
}
