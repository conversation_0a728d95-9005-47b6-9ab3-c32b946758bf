import { auth } from "@/auth";

import { NextRequest } from "next/server";
import { generateSeoInfo } from "ui/src/utils";

export async function POST(request: NextRequest) {
  const res = await auth();
  if (!res?.user || res.user.role !== "ADMIN") {
    return Response.json({ message: "Unauthorized" }, { status: 401 });
  }
  const { lang, title, article } = await request.json();
  const seoInfo = await generateSeoInfo({ lang, title, article });
  return Response.json(seoInfo);
}
