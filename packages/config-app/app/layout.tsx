import "./globals.css";
import { Nav } from "./components/nav";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body className="antialiased h-full bg-background text-foreground">
        <div className="h-full flex flex-col">
          <Nav />
          <div className="grow flex-auto flex">{children}</div>
        </div>
      </body>
    </html>
  );
}
