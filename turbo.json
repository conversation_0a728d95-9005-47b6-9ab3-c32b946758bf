{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}}}